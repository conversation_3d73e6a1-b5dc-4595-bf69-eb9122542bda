import { BookOpen, Calendar, User, AlertCircle } from 'lucide-react';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { useBrief } from '@/hooks/collaboration-hub-briefs';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';

interface BriefViewDialogProps {
  briefId: number | null;
  hubId: number;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function BriefViewDialog({ 
  briefId, 
  hubId, 
  open, 
  onOpenChange 
}: BriefViewDialogProps) {
  const { t, keys } = useTranslations();
  const isMobile = useIsMobile();

  // Fetch brief details when dialog opens
  const {
    data: brief,
    isLoading,
    isError
  } = useBrief(hubId, briefId, {
    enabled: !!briefId && open
  });

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatRelativeDate = (dateString: string) => {
    return formatDistanceToNow(new Date(dateString), { addSuffix: true });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        className={cn(
          "max-w-4xl w-full",
          isMobile && "h-full max-h-screen m-0 rounded-none"
        )}
        aria-describedby="brief-dialog-description"
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-primary" />
            {isLoading ? (
              <Skeleton className="h-6 w-48" />
            ) : (
              brief?.title || t(keys.collaborationHubs.briefs.viewDialog.title)
            )}
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className={cn("flex-1", isMobile ? "h-[calc(100vh-8rem)]" : "max-h-[70vh]")}>
          <div id="brief-dialog-description" className="space-y-6 p-1">
            {/* Loading State */}
            {isLoading && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      <Skeleton className="h-6 w-32" />
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Skeleton className="h-4 w-20 mb-2" />
                        <Skeleton className="h-5 w-32" />
                      </div>
                      <div>
                        <Skeleton className="h-4 w-24 mb-2" />
                        <Skeleton className="h-5 w-40" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      <Skeleton className="h-6 w-24" />
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Error State */}
            {isError && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {t(keys.collaborationHubs.briefs.viewDialog.error)}
                </AlertDescription>
              </Alert>
            )}

            {/* Content */}
            {brief && !isLoading && !isError && (
              <div className="space-y-6">
                {/* Basic Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <BookOpen className="h-5 w-5" />
                      {t(keys.collaborationHubs.briefs.viewDialog.basicInfo)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          {t(keys.collaborationHubs.briefs.viewDialog.createdAt)}
                        </label>
                        <p className="text-sm font-semibold">
                          {formatDate(brief.createdAt)}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatRelativeDate(brief.createdAt)}
                        </p>
                      </div>
                      
                      {brief.updatedAt !== brief.createdAt && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">
                            {t(keys.collaborationHubs.briefs.viewDialog.updatedAt)}
                          </label>
                          <p className="text-sm font-semibold">
                            {formatDate(brief.updatedAt)}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {formatRelativeDate(brief.updatedAt)}
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Creator Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <User className="h-5 w-5" />
                      {t(keys.collaborationHubs.briefs.viewDialog.creatorInfo)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback className="text-sm">
                          {getInitials(brief.createdByParticipantName || "Unknown")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-semibold">
                          {brief.createdByParticipantName || "Unknown"}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {t(keys.collaborationHubs.briefs.viewDialog.createdBy)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Content */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Calendar className="h-5 w-5" />
                      {t(keys.collaborationHubs.briefs.viewDialog.content)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {brief.body ? (
                      <div className="prose prose-sm max-w-none">
                        <div className="whitespace-pre-wrap text-sm leading-relaxed">
                          {brief.body}
                        </div>
                      </div>
                    ) : (
                      <p className="text-muted-foreground italic">
                        {t(keys.collaborationHubs.briefs.viewDialog.noContent)}
                      </p>
                    )}
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
